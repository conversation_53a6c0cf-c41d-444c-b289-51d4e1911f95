import { type LoaderFunctionArgs, type ActionFunctionArgs } from '@remix-run/node'
import { useLoaderData, Link, Form, useActionData } from '@remix-run/react'
import { Button } from '~/components/ui/button'
import { ArrowLeft, Copy, ExternalLink, <PERSON>otateCcw, Trash } from 'lucide-react'
import {
  getOrganization,
  getOrganizationUsers,
  deleteOrganizationUser,
  OrganizationError,
} from '~/lib/organization.server'
import { requireAdminAccess } from '~/lib/session.server'
import { deleteUserTests } from '~/lib/test.server'

interface OrganizationUserData {
  id: string
  organizationId: string
  userId: string
  testId?: string
  createdAt: Date
  User: {
    id: string
    name: string
    email?: string
    phoneNumber?: string
    createdAt: Date
  }
  Test?: {
    id: string
    url: string
    createdAt: Date
  }
}

export async function action({ request }: ActionFunctionArgs) {
  // Check if user has admin access
  await requireAdminAccess(request)

  const formData = await request.formData()
  const action = formData.get('action')?.toString()
  const userEmail = formData.get('userEmail')?.toString()
  const userId = formData.get('userId')?.toString()
  const orgId = formData.get('orgId')?.toString()

  if (action === 'reset-tests') {
    if (!userEmail || !orgId) {
      return Response.json(
        { error: 'User email and organization ID are required' },
        { status: 400 },
      )
    }

    try {
      const deletedCount = await deleteUserTests(userEmail)

      if (deletedCount === 0) {
        return Response.json({
          warning: `No tests found for ${userEmail}`,
        })
      }

      return Response.json({
        success: true,
        message: `Successfully reset ${deletedCount} test(s) for ${userEmail}`,
      })
    } catch (error) {
      return Response.json({ error: 'Failed to reset tests' }, { status: 500 })
    }
  }

  if (action === 'delete-user') {
    if (!userId || !orgId) {
      return Response.json({ error: 'User ID and organization ID are required' }, { status: 400 })
    }

    try {
      // First delete the user's tests
      if (userEmail) {
        await deleteUserTests(userEmail)
      }

      // Then delete the organization user record
      await deleteOrganizationUser(orgId, userId)

      return Response.json({
        success: true,
        message: `Successfully removed user from organization`,
      })
    } catch (error) {
      return Response.json({ error: 'Failed to delete user' }, { status: 500 })
    }
  }

  return Response.json({ error: 'Invalid action' }, { status: 400 })
}

export async function loader({ request }: LoaderFunctionArgs) {
  // Check if user has admin access
  await requireAdminAccess(request)

  const url = new URL(request.url)
  const orgId = url.searchParams.get('id')

  if (!orgId) {
    throw new Response('Organization ID is required', { status: 400 })
  }

  try {
    const organization = await getOrganization(orgId)
    if (!organization.isOpenRegistration) {
      throw new Response('This organization does not have open registration', {
        status: 403,
      })
    }

    const organizationUsers = await getOrganizationUsers(orgId)

    return Response.json({
      organization: {
        id: organization.id,
        name: organization.name,
        isOpenRegistration: organization.isOpenRegistration,
      },
      users: organizationUsers.map((ou: any) => ({
        id: ou.id,
        organizationId: ou.organizationId,
        userId: ou.userId,
        testId: ou.testId,
        createdAt: ou.createdAt,
        User: ou.User,
        Test: ou.Test,
      })),
    })
  } catch (error) {
    if (error instanceof OrganizationError) {
      throw new Response(error.message, { status: 404 })
    }
    throw error
  }
}

export default function OrganizationUsers() {
  const { organization, users } = useLoaderData<typeof loader>()
  const actionData = useActionData<typeof action>()

  return (
    <div className="container mx-auto">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <div className="flex items-center gap-4 mb-2">
            <Link to="/pea-admin/organizations">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="size-6" />
              </Button>
            </Link>
          </div>
          <h1 className="text-2xl font-semibold text-gray-900">
            Registered Users - {organization.name}
          </h1>
          <p className="text-gray-600 mt-1">
            Users who registered through the open registration link
          </p>
        </div>
      </div>

      {/* Action Messages */}
      {actionData?.success && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <p className="text-green-600 text-sm">{actionData.message}</p>
        </div>
      )}
      {actionData?.warning && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-yellow-600 text-sm">{actionData.warning}</p>
        </div>
      )}
      {actionData?.error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600 text-sm">{actionData.error}</p>
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Total Users: {users.length}</h2>
        </div>

        {users.length === 0 ? (
          <div className="px-6 py-8 text-center">
            <p className="text-gray-500">No users have registered yet.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Test ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Registration Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user: OrganizationUserData) => (
                  <tr key={user.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {user.User?.name || user.User?.email?.split('@')[0] || 'Unknown User'}
                      </div>
                      <div className="text-sm text-gray-500">ID: {user.User?.id || 'N/A'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {user.User?.email && <div>{user.User.email}</div>}
                        {user.User?.phoneNumber && <div>📱 {user.User.phoneNumber}</div>}
                        {!user.User?.email && !user.User?.phoneNumber && (
                          <div className="text-gray-500">No contact info</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.Test ? (
                        <div>{user.Test.id}</div>
                      ) : (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          No Test
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(user.createdAt).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        {user.Test && (
                          <>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="py-1 px-2 text-xs"
                              onClick={() => {
                                const url = user.Test!.url
                                navigator.clipboard.writeText(url)
                              }}
                              title="Copy Test URL"
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                            <Form method="post">
                              <input type="hidden" name="action" value="reset-tests" />
                              <input type="hidden" name="userEmail" value={user.User?.email} />
                              <input type="hidden" name="orgId" value={organization.id} />
                              <Button
                                type="submit"
                                variant="outline"
                                size="sm"
                                className="text-amber-500 hover:text-amber-700 hover:bg-amber-50"
                                title="Reset Tests"
                              >
                                <RotateCcw className="w-3 h-3" />
                              </Button>
                            </Form>
                          </>
                        )}
                        <Form method="post">
                          <input type="hidden" name="action" value="delete-user" />
                          <input type="hidden" name="userId" value={user.User?.id} />
                          <input type="hidden" name="userEmail" value={user.User?.email} />
                          <input type="hidden" name="orgId" value={organization.id} />
                          <Button
                            type="submit"
                            variant="outline"
                            size="sm"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            title="Remove User"
                            onClick={(e) => {
                              if (
                                !confirm(
                                  `Are you sure you want to remove ${user.User?.name || user.User?.email} from this organization?`,
                                )
                              ) {
                                e.preventDefault()
                              }
                            }}
                          >
                            <Trash className="w-3 h-3" />
                          </Button>
                        </Form>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}

import { DataTypes, Model, Sequelize, InferAttributes, InferCreationAttributes } from 'sequelize'

export class OrganizationInstance extends Model<
  InferAttributes<OrganizationInstance>,
  InferCreationAttributes<OrganizationInstance>
> {
  declare id: string
  declare name: string
  declare problemSetId: string
  declare isOpenRegistration: boolean
  declare maxRegistrations?: number
  declare createdAt?: Date
  declare updatedAt?: Date

  static initialize(sequelize: Sequelize) {
    return this.init(
      {
        id: {
          type: DataTypes.STRING,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        problemSetId: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'problem_set_id',
        },
        isOpenRegistration: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          field: 'is_open_registration',
        },
        maxRegistrations: {
          type: DataTypes.INTEGER,
          allowNull: true,
          field: 'max_registrations',
          validate: {
            min: 1,
          },
        },
        createdAt: {
          type: DataTypes.DATE,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          field: 'updated_at',
        },
      },
      {
        sequelize,
        tableName: 'organizations',
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    )
  }
}

export function OrganizationModel(sequelize: Sequelize) {
  return OrganizationInstance.initialize(sequelize)
} 
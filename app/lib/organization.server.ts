import { Organization, ProblemSet, OrganizationUser, User, Test } from "~/models";
import { v4 as uuidv4 } from "uuid";
import { nanoid } from "nanoid";

export class OrganizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "OrganizationError";
  }
}

export async function createOrganization(
  name: string,
  problemSetId: string,
  customId?: string,
  isOpenRegistration: boolean = false,
  maxRegistrations?: number
) {
  // Verify the problem set exists
  const problemSet = await ProblemSet.findByPk(problemSetId);
  if (!problemSet) {
    throw new OrganizationError("Problem set not found");
  }

  // Use custom ID if provided, otherwise generate a UUID
  const id = customId || uuidv4();

  // Check if an organization with this ID already exists
  if (customId) {
    const existingOrg = await Organization.findByPk(customId);
    if (existingOrg) {
      throw new OrganizationError(
        `Organization with ID ${customId} already exists`
      );
    }
  }

  const organization = await Organization.create({
    id,
    name,
    problemSetId,
    isOpenRegistration,
    maxRegistrations,
  });

  return organization;
}

export async function getOrganization(id: string) {
  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }
  return organization;
}

export async function updateOrganizationProblemSet(
  id: string,
  problemSetId: string
) {
  // Verify the problem set exists
  const problemSet = await ProblemSet.findByPk(problemSetId);
  if (!problemSet) {
    throw new OrganizationError("Problem set not found");
  }

  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  organization.problemSetId = problemSetId;
  await organization.save();

  return organization;
}

export async function getOrganizationProblemSetId(
  orgId: string
): Promise<string> {
  const organization = await Organization.findByPk(orgId);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  return organization.problemSetId;
}

export async function deleteOrganization(id: string) {
  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  await organization.destroy();
  return true;
}

export async function createOrganizationUser(
  organizationId: string,
  userId: string,
  testId?: string
) {
  const organizationUser = await OrganizationUser.create({
    id: nanoid(),
    organizationId,
    userId,
    testId,
  });

  return organizationUser;
}

export async function getOrganizationUser(
  organizationId: string,
  userId: string
) {
  return OrganizationUser.findOne({
    where: {
      organizationId,
      userId,
    },
  });
}

export async function updateOrganizationUserTest(
  organizationId: string,
  userId: string,
  testId: string
) {
  const organizationUser = await getOrganizationUser(organizationId, userId);
  if (!organizationUser) {
    throw new OrganizationError("Organization user not found");
  }

  organizationUser.testId = testId;
  await organizationUser.save();

  return organizationUser;
}

export async function getOrganizationUsers(organizationId: string) {
  const results = await OrganizationUser.findAll({
    where: { organizationId },
    include: [
      {
        model: User,
        as: 'User',
        attributes: ['id', 'name', 'email', 'phoneNumber'],
      },
      {
        model: Test,
        as: 'Test',
        attributes: ['id', 'url', 'createdAt'],
        required: false,
      },
    ],
    order: [['createdAt', 'DESC']],
  });

  return results;
}

export async function getOrganizationUsersCount(organizationId: string) {
  return OrganizationUser.count({
    where: { organizationId },
  });
}

export async function deleteOrganizationUser(
  organizationId: string,
  userId: string
) {
  const organizationUser = await getOrganizationUser(organizationId, userId);
  if (!organizationUser) {
    throw new OrganizationError("Organization user not found");
  }

  await organizationUser.destroy();
  return true;
}

export async function checkRegistrationLimit(organizationId: string) {
  const organization = await getOrganization(organizationId);

  // If no limit is set, registration is allowed
  if (!organization.maxRegistrations) {
    return { canRegister: true, currentCount: 0, maxCount: null };
  }

  const currentCount = await getOrganizationUsersCount(organizationId);
  const canRegister = currentCount < organization.maxRegistrations;

  return {
    canRegister,
    currentCount,
    maxCount: organization.maxRegistrations,
  };
}

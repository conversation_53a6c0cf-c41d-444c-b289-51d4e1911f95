#!/usr/bin/env node

/**
 * Production Migration Script for Open Registration Feature
 *
 * This script safely migrates the production SQLite database to support:
 * - Open registration functionality
 * - Registration limit feature
 * - Organization user management
 *
 * Version: 2.0
 * Features: Open Registration + Registration Limits
 */

import { Sequelize } from 'sequelize';
import fs from 'fs';
import path from 'path';

// Production database configuration
const DB_PATH = process.env.DATABASE_URL || './data/sqlite.db';
const BACKUP_DIR = './backups';

// Ensure backup directory exists
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Create timestamped backup
function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = path.join(BACKUP_DIR, `database_backup_${timestamp}.sqlite`);
  
  console.log('📦 Creating database backup...');
  fs.copyFileSync(DB_PATH, backupPath);
  console.log(`✅ Backup created: ${backupPath}`);
  
  return backupPath;
}

// Initialize Sequelize connection
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: DB_PATH,
  logging: false, // Disable SQL logging in production
});

async function checkCurrentSchema() {
  console.log('🔍 Checking current database schema...');

  try {
    // Check if organizations table has required columns
    const [results] = await sequelize.query("PRAGMA table_info(organizations)");
    const hasOpenRegistration = results.some(col => col.name === 'is_open_registration');
    const hasMaxRegistrations = results.some(col => col.name === 'max_registrations');

    // Check if organization_users table exists
    const [tables] = await sequelize.query("SELECT name FROM sqlite_master WHERE type='table' AND name='organization_users'");
    const hasOrgUsersTable = tables.length > 0;

    return {
      hasOpenRegistration,
      hasMaxRegistrations,
      hasOrgUsersTable,
      needsMigration: !hasOpenRegistration || !hasMaxRegistrations || !hasOrgUsersTable
    };
  } catch (error) {
    console.error('❌ Error checking schema:', error);
    throw error;
  }
}

async function addOpenRegistrationColumn() {
  console.log('📝 Adding is_open_registration column to organizations table...');

  try {
    await sequelize.query(`
      ALTER TABLE organizations
      ADD COLUMN is_open_registration BOOLEAN DEFAULT 0
    `);
    console.log('✅ Added is_open_registration column');
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('ℹ️  is_open_registration column already exists');
    } else {
      throw error;
    }
  }
}

async function addMaxRegistrationsColumn() {
  console.log('📝 Adding max_registrations column to organizations table...');

  try {
    await sequelize.query(`
      ALTER TABLE organizations
      ADD COLUMN max_registrations INTEGER
    `);
    console.log('✅ Added max_registrations column');
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('ℹ️  max_registrations column already exists');
    } else {
      throw error;
    }
  }
}

async function createOrganizationUsersTable() {
  console.log('📝 Creating organization_users table...');

  try {
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS organization_users (
        id TEXT PRIMARY KEY,
        organization_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        test_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE SET NULL,
        UNIQUE(organization_id, user_id)
      )
    `);
    console.log('✅ Created organization_users table');
  } catch (error) {
    console.error('❌ Error creating organization_users table:', error);
    throw error;
  }
}

async function createIndexes() {
  console.log('📝 Creating database indexes...');
  
  try {
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_organization_users_org_id
      ON organization_users(organization_id)
    `);

    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_organization_users_user_id
      ON organization_users(user_id)
    `);
    
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_organizations_open_registration
      ON organizations(is_open_registration)
    `);
    
    console.log('✅ Created database indexes');
  } catch (error) {
    console.error('❌ Error creating indexes:', error);
    throw error;
  }
}

async function verifyMigration() {
  console.log('🔍 Verifying migration...');

  try {
    // Verify organizations table structure
    const [orgColumns] = await sequelize.query("PRAGMA table_info(organizations)");
    const hasOpenReg = orgColumns.some(col => col.name === 'is_open_registration');
    const hasMaxReg = orgColumns.some(col => col.name === 'max_registrations');

    if (!hasOpenReg) {
      throw new Error('is_open_registration column not found in organizations table');
    }

    if (!hasMaxReg) {
      throw new Error('max_registrations column not found in organizations table');
    }

    // Verify organization_users table
    const [orgUserColumns] = await sequelize.query("PRAGMA table_info(organization_users)");
    const expectedColumns = ['id', 'organization_id', 'user_id', 'test_id', 'created_at'];

    for (const col of expectedColumns) {
      if (!orgUserColumns.some(dbCol => dbCol.name === col)) {
        throw new Error(`Column ${col} not found in organization_users table`);
      }
    }

    // Test basic operations
    await sequelize.query("SELECT COUNT(*) FROM organizations");
    await sequelize.query("SELECT COUNT(*) FROM organization_users");

    // Verify indexes exist
    const [indexes] = await sequelize.query("SELECT name FROM sqlite_master WHERE type='index'");
    const expectedIndexes = [
      'idx_organization_users_org_id',
      'idx_organization_users_user_id',
      'idx_organizations_open_registration'
    ];

    for (const indexName of expectedIndexes) {
      if (!indexes.some(idx => idx.name === indexName)) {
        console.warn(`⚠️  Index ${indexName} not found (this is not critical)`);
      }
    }

    console.log('✅ Migration verification successful');
    return true;
  } catch (error) {
    console.error('❌ Migration verification failed:', error);
    throw error;
  }
}

async function showMigrationSummary() {
  console.log('\n📊 Migration Summary:');
  
  try {
    const [orgCount] = await sequelize.query("SELECT COUNT(*) as count FROM organizations");
    const [userCount] = await sequelize.query("SELECT COUNT(*) as count FROM organization_users");
    const [openRegCount] = await sequelize.query("SELECT COUNT(*) as count FROM organizations WHERE is_open_registration = 1");
    const [limitedRegCount] = await sequelize.query("SELECT COUNT(*) as count FROM organizations WHERE max_registrations IS NOT NULL");

    console.log(`   • Total organizations: ${orgCount[0].count}`);
    console.log(`   • Open registration organizations: ${openRegCount[0].count}`);
    console.log(`   • Organizations with registration limits: ${limitedRegCount[0].count}`);
    console.log(`   • Organization users: ${userCount[0].count}`);
  } catch (error) {
    console.error('❌ Error generating summary:', error);
  }
}

async function runMigration() {
  let backupPath = null;
  
  try {
    console.log('🚀 Starting production database migration...\n');
    
    // Step 1: Create backup
    backupPath = createBackup();
    
    // Step 2: Connect to database
    console.log('🔌 Connecting to database...');
    await sequelize.authenticate();
    console.log('✅ Database connection established');
    
    // Step 3: Check current schema
    const schemaStatus = await checkCurrentSchema();
    
    if (!schemaStatus.needsMigration) {
      console.log('ℹ️  Database is already up to date. No migration needed.');
      await showMigrationSummary();
      return;
    }
    
    console.log('📋 Migration needed. Starting schema updates...\n');
    
    // Step 4: Run migrations
    if (!schemaStatus.hasOpenRegistration) {
      await addOpenRegistrationColumn();
    }

    if (!schemaStatus.hasMaxRegistrations) {
      await addMaxRegistrationsColumn();
    }

    if (!schemaStatus.hasOrgUsersTable) {
      await createOrganizationUsersTable();
    }
    
    // Step 5: Create indexes
    await createIndexes();
    
    // Step 6: Verify migration
    await verifyMigration();
    
    // Step 7: Show summary
    await showMigrationSummary();
    
    console.log('\n🎉 Production migration completed successfully!');
    console.log(`📦 Backup available at: ${backupPath}`);
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    
    if (backupPath) {
      console.log('\n🔄 To rollback, restore from backup:');
      console.log(`   cp "${backupPath}" "${DB_PATH}"`);
    }
    
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run migration
runMigration().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
